#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进的POI计算器
主要解决原有POI计算中的重复计算问题，提升计算有效性
"""

import os
import json
import pandas as pd
import numpy as np
from collections import defaultdict
from station_analysis import StationAnalyzer

class ImprovedPOICalculator:
    """改进的POI计算器，解决重复计算问题并支持距离权重"""

    def __init__(self, use_distance_weight=False, decay_factor=1000, max_distance=3000, min_weight=0.1):
        """初始化计算器

        参数:
            use_distance_weight: 是否使用距离权重
            decay_factor: 距离衰减因子（米）
            max_distance: 最大有效距离（米）
            min_weight: 最小权重值
        """
        self.analyzer = None
        self.improved_vectors = None
        self.original_vectors = None
        self.distance_weighted_vectors = None

        # 距离权重参数
        self.use_distance_weight = use_distance_weight
        self.decay_factor = decay_factor
        self.max_distance = max_distance
        self.min_weight = min_weight

        if use_distance_weight:
            print(f"启用距离权重: 衰减因子={decay_factor}m, 最大距离={max_distance}m, 最小权重={min_weight}")
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.analyzer = StationAnalyzer()
        self.analyzer.load_data()
        print("数据加载完成")

    def calculate_distance_weight(self, distance):
        """计算距离权重

        参数:
            distance: 距离（米）

        返回:
            距离权重 (min_weight ~ 1.0)
        """
        if not self.use_distance_weight:
            return 1.0

        if distance > self.max_distance:
            return self.min_weight

        # 使用指数衰减函数
        weight = np.exp(-distance / self.decay_factor)
        return max(self.min_weight, weight)

    def build_improved_poi_vectors(self):
        """构建改进的POI向量，避免重复计算并支持距离权重"""
        if self.analyzer is None:
            self.load_data()

        method_desc = "改进的POI向量（避免重复计算"
        if self.use_distance_weight:
            method_desc += " + 距离权重"
        method_desc += "）"
        print(f"正在构建{method_desc}...")

        # 创建一个字典，用于存储每个场站的POI向量
        station_poi_vectors = {}

        # 对于每个场站
        for station_name, station_info in self.analyzer.raw_poi_data.items():
            # 初始化POI向量
            poi_vector = defaultdict(float)  # 使用float支持距离权重

            # 获取场站周围的POI数据
            pois = station_info['data'].get('pois', [])

            # 统计每个POI类别的数量，避免重复计算
            for poi in pois:
                if 'typecode' in poi:
                    typecode = poi['typecode']

                    # 计算权重（距离权重或1.0）
                    distance = float(poi.get('distance', 0))
                    weight = self.calculate_distance_weight(distance)

                    # 获取大类和中类编码
                    if len(typecode) == 6:  # 确保是6位编码
                        large_category = typecode[:2] + '0000'  # 大类
                        medium_category = typecode[:4] + '00'   # 中类

                        # 优先使用最具体的编码，避免重复计算
                        # 这是关键改进：只在最具体的层次计数一次
                        if typecode in self.analyzer.used_poi_codes:
                            poi_vector[typecode] += weight
                        elif medium_category in self.analyzer.used_poi_codes:
                            poi_vector[medium_category] += weight
                        elif large_category in self.analyzer.used_poi_codes:
                            poi_vector[large_category] += weight

            # 将defaultdict转换为普通dict并存储
            station_poi_vectors[station_name] = dict(poi_vector)

        # 创建一个DataFrame，行是场站，列是POI类别
        all_typecodes = sorted(self.analyzer.used_poi_codes)
        stations = sorted(station_poi_vectors.keys())

        # 创建一个空的DataFrame
        self.improved_vectors = pd.DataFrame(0.0, index=stations, columns=all_typecodes)

        # 填充DataFrame
        for station in stations:
            for typecode, count in station_poi_vectors[station].items():
                if typecode in all_typecodes:
                    self.improved_vectors.at[station, typecode] = count

        print(f"已为 {len(stations)} 个场站构建改进POI向量，包含 {len(all_typecodes)} 个POI类别")
        if self.use_distance_weight:
            print(f"距离权重已启用: 衰减因子={self.decay_factor}m")

        return self.improved_vectors
    
    def build_original_poi_vectors(self):
        """构建原有的POI向量（存在重复计算）"""
        if self.analyzer is None:
            self.load_data()
            
        print("正在构建原有的POI向量（存在重复计算）...")
        
        # 使用原有方法构建POI向量
        self.original_vectors = self.analyzer.build_poi_vectors()
        
        return self.original_vectors
    
    def compare_methods(self):
        """对比原有方法和改进方法"""
        print("\n开始对比原有方法和改进方法...")
        
        # 构建两种向量
        if self.original_vectors is None:
            self.build_original_poi_vectors()
        if self.improved_vectors is None:
            self.build_improved_poi_vectors()
        
        # 分析重复计算的影响
        self._analyze_duplicate_calculation_impact()
        
        # 对比评分效果
        self._compare_scoring_effects()
        
        # 生成对比报告
        self._generate_comparison_report()
    
    def _analyze_duplicate_calculation_impact(self):
        """分析重复计算的影响"""
        print("\n分析重复计算的影响...")
        
        # 计算每个站点的总POI数量
        original_totals = self.original_vectors.sum(axis=1)
        improved_totals = self.improved_vectors.sum(axis=1)
        
        # 计算重复计算的比例
        duplicate_ratios = (original_totals - improved_totals) / original_totals * 100
        
        print(f"平均重复计算比例: {duplicate_ratios.mean():.1f}%")
        print(f"最大重复计算比例: {duplicate_ratios.max():.1f}%")
        print(f"最小重复计算比例: {duplicate_ratios.min():.1f}%")
        
        # 找出重复计算最严重的站点
        worst_stations = duplicate_ratios.nlargest(5)
        print("\n重复计算最严重的5个站点:")
        for station, ratio in worst_stations.items():
            original_count = original_totals[station]
            improved_count = improved_totals[station]
            print(f"{station}: {ratio:.1f}% (原有:{original_count}, 改进:{improved_count})")
        
        return duplicate_ratios
    
    def _compare_scoring_effects(self):
        """对比评分效果"""
        print("\n对比评分效果...")
        
        # 计算原有方法的评分
        original_scores = []
        for station in self.original_vectors.index:
            score = self.analyzer.evaluate_station_score(station)
            original_scores.append(score)
        
        # 计算改进方法的评分
        improved_scores = []
        for station in self.improved_vectors.index:
            if station in self.original_vectors.index:
                # 临时替换POI向量来计算改进方法的评分
                original_poi_vectors = self.analyzer.poi_vectors
                self.analyzer.poi_vectors = self.improved_vectors
                score = self.analyzer.evaluate_station_score(station)
                improved_scores.append(score)
                # 恢复原有POI向量
                self.analyzer.poi_vectors = original_poi_vectors
            else:
                improved_scores.append(0)
        
        # 计算统计指标
        original_stats = pd.Series(original_scores).describe()
        improved_stats = pd.Series(improved_scores).describe()
        
        print("评分统计对比:")
        print(f"{'指标':<10} {'原有方法':<12} {'改进方法':<12} {'变化':<10}")
        print("-" * 50)
        print(f"{'平均分':<10} {original_stats['mean']:<12.2f} {improved_stats['mean']:<12.2f} {(improved_stats['mean']-original_stats['mean'])/original_stats['mean']*100:>8.1f}%")
        print(f"{'标准差':<10} {original_stats['std']:<12.2f} {improved_stats['std']:<12.2f} {(improved_stats['std']-original_stats['std'])/original_stats['std']*100:>8.1f}%")
        print(f"{'最大值':<10} {original_stats['max']:<12.2f} {improved_stats['max']:<12.2f} {(improved_stats['max']-original_stats['max'])/original_stats['max']*100:>8.1f}%")
        print(f"{'最小值':<10} {original_stats['min']:<12.2f} {improved_stats['min']:<12.2f} {(improved_stats['min']-original_stats['min'])/original_stats['min']*100:>8.1f}%")
        
        # 计算相关性
        correlation = pd.Series(original_scores).corr(pd.Series(improved_scores))
        print(f"\n评分相关性: {correlation:.3f}")
        
        return original_scores, improved_scores
    
    def _generate_comparison_report(self):
        """生成对比报告"""
        print("\n生成对比报告...")
        
        # 计算重复计算影响
        duplicate_ratios = self._analyze_duplicate_calculation_impact()
        
        # 计算评分对比
        original_scores, improved_scores = self._compare_scoring_effects()
        
        # 生成报告内容
        report = []
        report.append("# POI计算改进报告 - 解决重复计算问题\n")
        
        report.append("## 1. 问题描述\n")
        report.append("原有的POI计算方法存在重复计算问题：")
        report.append("- 同一个POI在大类、中类、小类层次中被重复统计")
        report.append("- 导致POI数量被人为放大，影响评分准确性")
        report.append("- 不同层次的POI编码权重叠加，造成评分偏差\n")
        
        report.append("## 2. 改进方案\n")
        report.append("采用优先级策略避免重复计算：")
        report.append("- 优先使用最具体的POI编码（6位完整编码）")
        report.append("- 如果具体编码不在使用列表中，则使用中类编码（4位+00）")
        report.append("- 如果中类编码也不在使用列表中，则使用大类编码（2位+0000）")
        report.append("- 确保每个POI只在一个层次被计算一次\n")
        
        report.append("## 3. 改进效果\n")
        
        # 重复计算统计
        report.append("### 3.1 重复计算减少效果")
        report.append(f"- 平均重复计算比例: {duplicate_ratios.mean():.1f}%")
        report.append(f"- 最大重复计算比例: {duplicate_ratios.max():.1f}%")
        report.append(f"- 重复计算问题最严重的站点数量: {(duplicate_ratios > 20).sum()} 个\n")
        
        # 评分对比统计
        original_stats = pd.Series(original_scores).describe()
        improved_stats = pd.Series(improved_scores).describe()
        correlation = pd.Series(original_scores).corr(pd.Series(improved_scores))
        
        report.append("### 3.2 评分效果对比")
        report.append("| 指标 | 原有方法 | 改进方法 | 变化幅度 |")
        report.append("|------|----------|----------|----------|")
        report.append(f"| 平均分 | {original_stats['mean']:.2f} | {improved_stats['mean']:.2f} | {(improved_stats['mean']-original_stats['mean'])/original_stats['mean']*100:+.1f}% |")
        report.append(f"| 标准差 | {original_stats['std']:.2f} | {improved_stats['std']:.2f} | {(improved_stats['std']-original_stats['std'])/original_stats['std']*100:+.1f}% |")
        report.append(f"| 评分相关性 | - | - | {correlation:.3f} |\n")
        
        report.append("## 4. 主要优势\n")
        report.append("1. **消除重复计算**: 避免同一POI在多个层次被重复统计")
        report.append("2. **提高准确性**: 评分更真实反映POI分布情况")
        report.append("3. **保持兼容性**: 不改变原有权重体系和评分逻辑")
        report.append("4. **简单有效**: 改进方案简单易懂，易于维护\n")
        
        report.append("## 5. 使用建议\n")
        report.append("1. 建议采用改进方法替代原有方法")
        report.append("2. 可以保留原有方法作为对比参考")
        report.append("3. 在权重优化时使用改进方法的结果")
        report.append("4. 定期检查POI编码使用情况，确保层次关系正确\n")
        
        # 保存报告
        os.makedirs('output', exist_ok=True)
        with open('output/poi_improvement_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("对比报告已保存到 output/poi_improvement_report.md")
    
    def save_improved_vectors(self, filepath='output/improved_poi_vectors.csv'):
        """保存改进的POI向量"""
        if self.improved_vectors is not None:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            self.improved_vectors.to_csv(filepath)
            print(f"改进的POI向量已保存到 {filepath}")
    
    def get_improvement_summary(self):
        """获取改进效果摘要"""
        if self.original_vectors is None or self.improved_vectors is None:
            print("请先运行对比分析")
            return None
        
        # 计算重复计算减少量
        original_totals = self.original_vectors.sum(axis=1)
        improved_totals = self.improved_vectors.sum(axis=1)
        duplicate_ratios = (original_totals - improved_totals) / original_totals * 100
        
        summary = {
            'average_duplicate_ratio': duplicate_ratios.mean(),
            'max_duplicate_ratio': duplicate_ratios.max(),
            'stations_with_high_duplicates': (duplicate_ratios > 20).sum(),
            'total_stations': len(duplicate_ratios),
            'improvement_coverage': (duplicate_ratios > 0).sum() / len(duplicate_ratios) * 100
        }
        
        return summary

    def integrate_with_station_scoring(self):
        """集成到场站评分体系中"""
        print("\n集成到场站评分体系...")

        if self.analyzer is None:
            self.load_data()

        # 构建改进的POI向量
        if self.improved_vectors is None:
            self.build_improved_poi_vectors()

        # 临时替换分析器的POI向量
        original_poi_vectors = self.analyzer.poi_vectors
        self.analyzer.poi_vectors = self.improved_vectors

        try:
            # 构建完整的评分框架
            scores_df = self.analyzer.build_scoring_framework(calculate_strategic=True)

            print(f"已为 {len(scores_df)} 个场站构建集成评分")
            print("\n集成评分前10名场站:")
            if 'combined_score' in scores_df.columns:
                top_stations = scores_df.head(10)[['station', 'poi_score', 'combined_score']]
            else:
                top_stations = scores_df.head(10)[['station', 'poi_score']]
            print(top_stations.to_string(index=False))

            # 保存集成评分结果
            scores_df.to_csv('output/integrated_improved_scores.csv', index=False)
            print("\n集成评分结果已保存到 output/integrated_improved_scores.csv")

            return scores_df

        finally:
            # 恢复原有POI向量
            self.analyzer.poi_vectors = original_poi_vectors

    def compare_all_methods(self):
        """对比所有方法：原有、改进（无距离权重）、改进（有距离权重）"""
        print("\n全面对比分析：原有方法 vs 改进方法 vs 距离权重方法")
        print("=" * 70)

        # 1. 原有方法（存在重复计算）
        print("\n1. 构建原有方法POI向量...")
        original_vectors = self.build_original_poi_vectors()

        # 2. 改进方法（无距离权重）
        print("\n2. 构建改进方法POI向量（无距离权重）...")
        current_distance_setting = self.use_distance_weight
        self.use_distance_weight = False
        improved_no_distance = self.build_improved_poi_vectors()

        # 3. 改进方法（有距离权重）
        print("\n3. 构建改进方法POI向量（有距离权重）...")
        self.use_distance_weight = True
        improved_with_distance = self.build_improved_poi_vectors()

        # 恢复原设置
        self.use_distance_weight = current_distance_setting

        # 计算各方法的评分
        methods = {
            '原有方法': original_vectors,
            '改进方法（无距离权重）': improved_no_distance,
            '改进方法（有距离权重）': improved_with_distance
        }

        scores_comparison = {}
        for method_name, vectors in methods.items():
            scores = []
            # 临时替换POI向量
            original_poi_vectors = self.analyzer.poi_vectors
            self.analyzer.poi_vectors = vectors

            for station in vectors.index:
                score = self.analyzer.evaluate_station_score(station)
                scores.append(score)

            # 恢复POI向量
            self.analyzer.poi_vectors = original_poi_vectors
            scores_comparison[method_name] = scores

        # 生成全面对比报告
        self._generate_comprehensive_comparison_report(methods, scores_comparison)

        return scores_comparison

    def _generate_comprehensive_comparison_report(self, methods, scores_comparison):
        """生成全面对比报告"""
        print("\n生成全面对比报告...")

        # 计算统计指标
        stats_comparison = {}
        for method_name, scores in scores_comparison.items():
            stats_comparison[method_name] = pd.Series(scores).describe()

        # 计算相关性矩阵
        correlation_matrix = pd.DataFrame(scores_comparison).corr()

        # 生成报告
        report = []
        report.append("# POI计算方法全面对比报告\n")

        report.append("## 1. 对比方法说明\n")
        report.append("### 原有方法")
        report.append("- 存在重复计算问题")
        report.append("- 不考虑POI距离因素")
        report.append("- 同一POI在多个层次被重复统计\n")

        report.append("### 改进方法（无距离权重）")
        report.append("- 解决重复计算问题")
        report.append("- 优先使用最具体的POI编码")
        report.append("- 不考虑POI距离因素\n")

        report.append("### 改进方法（有距离权重）")
        report.append("- 解决重复计算问题")
        report.append("- 考虑POI距离因素")
        report.append(f"- 使用指数衰减函数：weight = exp(-distance/{self.decay_factor})")
        report.append(f"- 距离权重参数：衰减因子={self.decay_factor}m, 最大距离={self.max_distance}m, 最小权重={self.min_weight}\n")

        report.append("## 2. 评分统计对比\n")
        report.append("| 方法 | 平均分 | 标准差 | 最大值 | 最小值 |")
        report.append("|------|--------|--------|--------|--------|")
        for method_name, stats in stats_comparison.items():
            report.append(f"| {method_name} | {stats['mean']:.2f} | {stats['std']:.2f} | {stats['max']:.2f} | {stats['min']:.2f} |")
        report.append("")

        report.append("## 3. 方法间相关性\n")
        report.append("| 方法对比 | 相关系数 |")
        report.append("|----------|----------|")
        methods_list = list(scores_comparison.keys())
        for i in range(len(methods_list)):
            for j in range(i+1, len(methods_list)):
                method1, method2 = methods_list[i], methods_list[j]
                corr = correlation_matrix.loc[method1, method2]
                report.append(f"| {method1} vs {method2} | {corr:.3f} |")
        report.append("")

        # 计算改进效果
        original_stats = stats_comparison['原有方法']
        improved_no_dist_stats = stats_comparison['改进方法（无距离权重）']
        improved_with_dist_stats = stats_comparison['改进方法（有距离权重）']

        report.append("## 4. 改进效果分析\n")
        report.append("### 4.1 解决重复计算的效果")
        avg_change1 = (improved_no_dist_stats['mean'] - original_stats['mean']) / original_stats['mean'] * 100
        std_change1 = (improved_no_dist_stats['std'] - original_stats['std']) / original_stats['std'] * 100
        report.append(f"- 平均分变化: {avg_change1:+.1f}%")
        report.append(f"- 标准差变化: {std_change1:+.1f}%")
        report.append(f"- 评分相关性: {correlation_matrix.loc['原有方法', '改进方法（无距离权重）']:.3f}\n")

        report.append("### 4.2 增加距离权重的效果")
        avg_change2 = (improved_with_dist_stats['mean'] - improved_no_dist_stats['mean']) / improved_no_dist_stats['mean'] * 100
        std_change2 = (improved_with_dist_stats['std'] - improved_no_dist_stats['std']) / improved_no_dist_stats['std'] * 100
        report.append(f"- 平均分变化: {avg_change2:+.1f}%")
        report.append(f"- 标准差变化: {std_change2:+.1f}%")
        report.append(f"- 评分相关性: {correlation_matrix.loc['改进方法（无距离权重）', '改进方法（有距离权重）']:.3f}\n")

        report.append("## 5. 推荐方案\n")
        report.append("基于以上分析，推荐使用**改进方法（有距离权重）**，理由：")
        report.append("1. **解决重复计算问题**：避免POI被重复统计")
        report.append("2. **考虑距离因素**：更符合用户实际行为")
        report.append("3. **保持评分一致性**：与原有方法保持较高相关性")
        report.append("4. **参数可调**：可根据业务需求调整距离权重参数\n")

        report.append("## 6. 实施建议\n")
        report.append("1. **逐步迁移**：先在测试环境验证效果")
        report.append("2. **参数调优**：根据实际业务数据调整距离权重参数")
        report.append("3. **效果监控**：定期评估改进效果")
        report.append("4. **权重优化**：结合订单数据进行权重优化\n")

        # 保存报告
        os.makedirs('output', exist_ok=True)
        with open('output/comprehensive_poi_comparison_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        print("全面对比报告已保存到 output/comprehensive_poi_comparison_report.md")

        # 保存相关性矩阵
        correlation_matrix.to_csv('output/methods_correlation_matrix.csv')
        print("方法相关性矩阵已保存到 output/methods_correlation_matrix.csv")

def main():
    """主函数，演示改进效果"""
    print("POI计算改进演示 - 集成重复计算修复和距离权重")
    print("=" * 60)

    # 演示1：基础改进（解决重复计算）
    print("\n=== 演示1：基础改进（解决重复计算） ===")
    calculator_basic = ImprovedPOICalculator(use_distance_weight=False)
    calculator_basic.load_data()
    calculator_basic.compare_methods()

    # 演示2：距离权重改进
    print("\n=== 演示2：距离权重改进 ===")
    calculator_distance = ImprovedPOICalculator(
        use_distance_weight=True,
        decay_factor=1000,
        max_distance=3000,
        min_weight=0.1
    )
    calculator_distance.load_data()

    # 演示3：全面对比三种方法
    print("\n=== 演示3：全面对比分析 ===")
    scores_comparison = calculator_distance.compare_all_methods()

    # 演示4：集成到场站评分体系
    print("\n=== 演示4：集成到场站评分体系 ===")
    integrated_scores = calculator_distance.integrate_with_station_scoring()

    # 保存所有结果
    print("\n=== 保存结果 ===")
    calculator_distance.save_improved_vectors('output/final_improved_poi_vectors.csv')

    # 显示最终摘要
    summary = calculator_distance.get_improvement_summary()
    if summary:
        print(f"\n最终改进效果摘要:")
        print(f"- 平均减少重复计算: {summary['average_duplicate_ratio']:.1f}%")
        print(f"- 最大减少重复计算: {summary['max_duplicate_ratio']:.1f}%")
        print(f"- 受益站点比例: {summary['improvement_coverage']:.1f}%")
        print(f"- 重复计算严重站点数: {summary['stations_with_high_duplicates']} 个")

    print(f"\n距离权重参数:")
    print(f"- 衰减因子: {calculator_distance.decay_factor}米")
    print(f"- 最大距离: {calculator_distance.max_distance}米")
    print(f"- 最小权重: {calculator_distance.min_weight}")

    print("\n集成改进演示完成！")
    print("生成的文件:")
    print("- output/comprehensive_poi_comparison_report.md (全面对比报告)")
    print("- output/integrated_improved_scores.csv (集成评分结果)")
    print("- output/final_improved_poi_vectors.csv (最终POI向量)")
    print("- output/methods_correlation_matrix.csv (方法相关性矩阵)")

def demo_distance_weight_only():
    """仅演示距离权重功能"""
    print("距离权重POI计算演示")
    print("=" * 40)

    calculator = ImprovedPOICalculator(
        use_distance_weight=True,
        decay_factor=800,   # 更快衰减
        max_distance=2500,  # 更小范围
        min_weight=0.05     # 更小最小权重
    )

    calculator.load_data()
    calculator.compare_methods()

    print("\n距离权重演示完成！")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "distance":
        demo_distance_weight_only()
    else:
        main()
