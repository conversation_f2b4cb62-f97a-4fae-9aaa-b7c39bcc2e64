#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
改进的POI计算器
主要解决原有POI计算中的重复计算问题，提升计算有效性
"""

import os
import json
import pandas as pd
import numpy as np
from collections import defaultdict
from station_analysis import StationAnalyzer

class ImprovedPOICalculator:
    """改进的POI计算器，解决重复计算问题"""
    
    def __init__(self):
        """初始化计算器"""
        self.analyzer = None
        self.improved_vectors = None
        self.original_vectors = None
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.analyzer = StationAnalyzer()
        self.analyzer.load_data()
        print("数据加载完成")
    
    def build_improved_poi_vectors(self):
        """构建改进的POI向量，避免重复计算"""
        if self.analyzer is None:
            self.load_data()
            
        print("正在构建改进的POI向量（避免重复计算）...")
        
        # 创建一个字典，用于存储每个场站的POI向量
        station_poi_vectors = {}
        
        # 对于每个场站
        for station_name, station_info in self.analyzer.raw_poi_data.items():
            # 初始化POI向量
            poi_vector = defaultdict(int)
            
            # 获取场站周围的POI数据
            pois = station_info['data'].get('pois', [])
            
            # 统计每个POI类别的数量，避免重复计算
            for poi in pois:
                if 'typecode' in poi:
                    typecode = poi['typecode']
                    
                    # 获取大类和中类编码
                    if len(typecode) == 6:  # 确保是6位编码
                        large_category = typecode[:2] + '0000'  # 大类
                        medium_category = typecode[:4] + '00'   # 中类
                        
                        # 优先使用最具体的编码，避免重复计算
                        # 这是关键改进：只在最具体的层次计数一次
                        if typecode in self.analyzer.used_poi_codes:
                            poi_vector[typecode] += 1
                        elif medium_category in self.analyzer.used_poi_codes:
                            poi_vector[medium_category] += 1
                        elif large_category in self.analyzer.used_poi_codes:
                            poi_vector[large_category] += 1
            
            # 将defaultdict转换为普通dict并存储
            station_poi_vectors[station_name] = dict(poi_vector)
        
        # 创建一个DataFrame，行是场站，列是POI类别
        all_typecodes = sorted(self.analyzer.used_poi_codes)
        stations = sorted(station_poi_vectors.keys())
        
        # 创建一个空的DataFrame
        self.improved_vectors = pd.DataFrame(0, index=stations, columns=all_typecodes)
        
        # 填充DataFrame
        for station in stations:
            for typecode, count in station_poi_vectors[station].items():
                if typecode in all_typecodes:
                    self.improved_vectors.at[station, typecode] = count
        
        print(f"已为 {len(stations)} 个场站构建改进POI向量，包含 {len(all_typecodes)} 个POI类别")
        
        return self.improved_vectors
    
    def build_original_poi_vectors(self):
        """构建原有的POI向量（存在重复计算）"""
        if self.analyzer is None:
            self.load_data()
            
        print("正在构建原有的POI向量（存在重复计算）...")
        
        # 使用原有方法构建POI向量
        self.original_vectors = self.analyzer.build_poi_vectors()
        
        return self.original_vectors
    
    def compare_methods(self):
        """对比原有方法和改进方法"""
        print("\n开始对比原有方法和改进方法...")
        
        # 构建两种向量
        if self.original_vectors is None:
            self.build_original_poi_vectors()
        if self.improved_vectors is None:
            self.build_improved_poi_vectors()
        
        # 分析重复计算的影响
        self._analyze_duplicate_calculation_impact()
        
        # 对比评分效果
        self._compare_scoring_effects()
        
        # 生成对比报告
        self._generate_comparison_report()
    
    def _analyze_duplicate_calculation_impact(self):
        """分析重复计算的影响"""
        print("\n分析重复计算的影响...")
        
        # 计算每个站点的总POI数量
        original_totals = self.original_vectors.sum(axis=1)
        improved_totals = self.improved_vectors.sum(axis=1)
        
        # 计算重复计算的比例
        duplicate_ratios = (original_totals - improved_totals) / original_totals * 100
        
        print(f"平均重复计算比例: {duplicate_ratios.mean():.1f}%")
        print(f"最大重复计算比例: {duplicate_ratios.max():.1f}%")
        print(f"最小重复计算比例: {duplicate_ratios.min():.1f}%")
        
        # 找出重复计算最严重的站点
        worst_stations = duplicate_ratios.nlargest(5)
        print("\n重复计算最严重的5个站点:")
        for station, ratio in worst_stations.items():
            original_count = original_totals[station]
            improved_count = improved_totals[station]
            print(f"{station}: {ratio:.1f}% (原有:{original_count}, 改进:{improved_count})")
        
        return duplicate_ratios
    
    def _compare_scoring_effects(self):
        """对比评分效果"""
        print("\n对比评分效果...")
        
        # 计算原有方法的评分
        original_scores = []
        for station in self.original_vectors.index:
            score = self.analyzer.evaluate_station_score(station)
            original_scores.append(score)
        
        # 计算改进方法的评分
        improved_scores = []
        for station in self.improved_vectors.index:
            if station in self.original_vectors.index:
                # 临时替换POI向量来计算改进方法的评分
                original_poi_vectors = self.analyzer.poi_vectors
                self.analyzer.poi_vectors = self.improved_vectors
                score = self.analyzer.evaluate_station_score(station)
                improved_scores.append(score)
                # 恢复原有POI向量
                self.analyzer.poi_vectors = original_poi_vectors
            else:
                improved_scores.append(0)
        
        # 计算统计指标
        original_stats = pd.Series(original_scores).describe()
        improved_stats = pd.Series(improved_scores).describe()
        
        print("评分统计对比:")
        print(f"{'指标':<10} {'原有方法':<12} {'改进方法':<12} {'变化':<10}")
        print("-" * 50)
        print(f"{'平均分':<10} {original_stats['mean']:<12.2f} {improved_stats['mean']:<12.2f} {(improved_stats['mean']-original_stats['mean'])/original_stats['mean']*100:>8.1f}%")
        print(f"{'标准差':<10} {original_stats['std']:<12.2f} {improved_stats['std']:<12.2f} {(improved_stats['std']-original_stats['std'])/original_stats['std']*100:>8.1f}%")
        print(f"{'最大值':<10} {original_stats['max']:<12.2f} {improved_stats['max']:<12.2f} {(improved_stats['max']-original_stats['max'])/original_stats['max']*100:>8.1f}%")
        print(f"{'最小值':<10} {original_stats['min']:<12.2f} {improved_stats['min']:<12.2f} {(improved_stats['min']-original_stats['min'])/original_stats['min']*100:>8.1f}%")
        
        # 计算相关性
        correlation = pd.Series(original_scores).corr(pd.Series(improved_scores))
        print(f"\n评分相关性: {correlation:.3f}")
        
        return original_scores, improved_scores
    
    def _generate_comparison_report(self):
        """生成对比报告"""
        print("\n生成对比报告...")
        
        # 计算重复计算影响
        duplicate_ratios = self._analyze_duplicate_calculation_impact()
        
        # 计算评分对比
        original_scores, improved_scores = self._compare_scoring_effects()
        
        # 生成报告内容
        report = []
        report.append("# POI计算改进报告 - 解决重复计算问题\n")
        
        report.append("## 1. 问题描述\n")
        report.append("原有的POI计算方法存在重复计算问题：")
        report.append("- 同一个POI在大类、中类、小类层次中被重复统计")
        report.append("- 导致POI数量被人为放大，影响评分准确性")
        report.append("- 不同层次的POI编码权重叠加，造成评分偏差\n")
        
        report.append("## 2. 改进方案\n")
        report.append("采用优先级策略避免重复计算：")
        report.append("- 优先使用最具体的POI编码（6位完整编码）")
        report.append("- 如果具体编码不在使用列表中，则使用中类编码（4位+00）")
        report.append("- 如果中类编码也不在使用列表中，则使用大类编码（2位+0000）")
        report.append("- 确保每个POI只在一个层次被计算一次\n")
        
        report.append("## 3. 改进效果\n")
        
        # 重复计算统计
        report.append("### 3.1 重复计算减少效果")
        report.append(f"- 平均重复计算比例: {duplicate_ratios.mean():.1f}%")
        report.append(f"- 最大重复计算比例: {duplicate_ratios.max():.1f}%")
        report.append(f"- 重复计算问题最严重的站点数量: {(duplicate_ratios > 20).sum()} 个\n")
        
        # 评分对比统计
        original_stats = pd.Series(original_scores).describe()
        improved_stats = pd.Series(improved_scores).describe()
        correlation = pd.Series(original_scores).corr(pd.Series(improved_scores))
        
        report.append("### 3.2 评分效果对比")
        report.append("| 指标 | 原有方法 | 改进方法 | 变化幅度 |")
        report.append("|------|----------|----------|----------|")
        report.append(f"| 平均分 | {original_stats['mean']:.2f} | {improved_stats['mean']:.2f} | {(improved_stats['mean']-original_stats['mean'])/original_stats['mean']*100:+.1f}% |")
        report.append(f"| 标准差 | {original_stats['std']:.2f} | {improved_stats['std']:.2f} | {(improved_stats['std']-original_stats['std'])/original_stats['std']*100:+.1f}% |")
        report.append(f"| 评分相关性 | - | - | {correlation:.3f} |\n")
        
        report.append("## 4. 主要优势\n")
        report.append("1. **消除重复计算**: 避免同一POI在多个层次被重复统计")
        report.append("2. **提高准确性**: 评分更真实反映POI分布情况")
        report.append("3. **保持兼容性**: 不改变原有权重体系和评分逻辑")
        report.append("4. **简单有效**: 改进方案简单易懂，易于维护\n")
        
        report.append("## 5. 使用建议\n")
        report.append("1. 建议采用改进方法替代原有方法")
        report.append("2. 可以保留原有方法作为对比参考")
        report.append("3. 在权重优化时使用改进方法的结果")
        report.append("4. 定期检查POI编码使用情况，确保层次关系正确\n")
        
        # 保存报告
        os.makedirs('output', exist_ok=True)
        with open('output/poi_improvement_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("对比报告已保存到 output/poi_improvement_report.md")
    
    def save_improved_vectors(self, filepath='output/improved_poi_vectors.csv'):
        """保存改进的POI向量"""
        if self.improved_vectors is not None:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            self.improved_vectors.to_csv(filepath)
            print(f"改进的POI向量已保存到 {filepath}")
    
    def get_improvement_summary(self):
        """获取改进效果摘要"""
        if self.original_vectors is None or self.improved_vectors is None:
            print("请先运行对比分析")
            return None
        
        # 计算重复计算减少量
        original_totals = self.original_vectors.sum(axis=1)
        improved_totals = self.improved_vectors.sum(axis=1)
        duplicate_ratios = (original_totals - improved_totals) / original_totals * 100
        
        summary = {
            'average_duplicate_ratio': duplicate_ratios.mean(),
            'max_duplicate_ratio': duplicate_ratios.max(),
            'stations_with_high_duplicates': (duplicate_ratios > 20).sum(),
            'total_stations': len(duplicate_ratios),
            'improvement_coverage': (duplicate_ratios > 0).sum() / len(duplicate_ratios) * 100
        }
        
        return summary

def main():
    """主函数，演示改进效果"""
    print("POI计算改进演示 - 解决重复计算问题")
    print("=" * 50)
    
    # 创建改进计算器
    calculator = ImprovedPOICalculator()
    
    # 加载数据
    calculator.load_data()
    
    # 对比两种方法
    calculator.compare_methods()
    
    # 保存改进结果
    calculator.save_improved_vectors()
    
    # 显示改进摘要
    summary = calculator.get_improvement_summary()
    if summary:
        print(f"\n改进效果摘要:")
        print(f"- 平均减少重复计算: {summary['average_duplicate_ratio']:.1f}%")
        print(f"- 最大减少重复计算: {summary['max_duplicate_ratio']:.1f}%")
        print(f"- 受益站点比例: {summary['improvement_coverage']:.1f}%")
        print(f"- 重复计算严重站点数: {summary['stations_with_high_duplicates']} 个")
    
    print("\n改进演示完成！")

if __name__ == "__main__":
    main()
